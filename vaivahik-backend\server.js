// File: vaivahik-backend/server.js

// Import necessary modules
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const cookieParser = require('cookie-parser');
const path = require('path');
const { PrismaClient, Prisma } = require('@prisma/client');
const { createServer } = require('http'); // Needed for Socket.IO
const { Server } = require('socket.io');  // Needed for Socket.IO
const { initializeChatHandlers } = require('./src/socket/chatHandler'); // Import chat handlers

// Import Sentry for error monitoring
const { initSentry, sentryRequestHandler, sentryTracingHandler } = require('./src/config/sentry');
require('dotenv').config();

// Set mock data environment variable based on NODE_ENV
if (process.env.NODE_ENV === 'development' && !process.env.USE_MOCK_DATA) {
  process.env.USE_MOCK_DATA = 'true';
}

// --- Initialize Prisma Client ---
const prisma = new PrismaClient({
    // log: ['query','info','warn','error'], // Uncomment for detailed logs
});

// --- Configuration ---
const PORT = process.env.PORT || 8080; // Use environment variable or default to 8080
// Define the origin for your frontend. Use '*' or 'true' for dev ONLY. Be specific in production.
const FRONTEND_ORIGIN = process.env.FRONTEND_URL || 'http://localhost:3000'; // Default to frontend port

console.log(`Using port ${PORT} for the server`); // Log the port being used

// --- Initialize Express App & HTTP Server for Socket.IO ---
const app = express();
const httpServer = createServer(app); // Use http server
const io = new Server(httpServer, {   // Attach Socket.IO to http server
    cors: {
        origin: FRONTEND_ORIGIN, // Allow frontend origin for Socket.IO
        methods: ["GET", "POST"],
        credentials: true
    }
});

// Initialize chat socket handlers
initializeChatHandlers(io, prisma);


// --- Core Middleware ---

// Initialize Sentry with proper error handling (FIXED: No more "No DSN provided" warnings)
const sentryInitialized = initSentry({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV || 'development'
});

if (sentryInitialized) {
    // Add Sentry request handler (must be first middleware)
    app.use(sentryRequestHandler);
    logger.info('✅ Sentry request tracking enabled');
} else {
    logger.info('ℹ️ Sentry disabled - no DSN provided (this is normal in development)');
}

// Import custom middleware
const standardizeResponse = require('./src/middleware/apiResponse.middleware');
const requestLogger = require('./src/middleware/requestLogger.middleware');
const { apiRateLimiter, authRateLimiter } = require('./src/middleware/rateLimit.middleware');
const logger = require('./src/utils/logger');

// CORS configuration
const corsOptions = {
    origin: process.env.NODE_ENV === 'production'
        ? [process.env.FRONTEND_URL || 'https://vaivahik.com'] // Specific origins in production
        : true, // Allow any origin in development
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
    maxAge: 86400 // 24 hours
};

// Apply CORS middleware
app.use(cors(corsOptions));

// Apply security headers with Helmet
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'", "data:", "https:"],
            imgSrc: ["'self'", "data:", "https:", "blob:", "placehold.co"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com", "https://code.jquery.com", "https://cdn.datatables.net"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com", "https://cdn.datatables.net"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            connectSrc: ["*"]
        }
    },
    xssFilter: true,
    noSniff: true,
    referrerPolicy: { policy: 'same-origin' }
}));

// Apply request logging middleware
app.use(requestLogger);

// Apply API response standardization middleware
app.use(standardizeResponse);

// Add Sentry tracing middleware
app.use(sentryTracingHandler);

// Apply rate limiting to auth routes
app.use('/api/auth', authRateLimiter);

// Apply rate limiting to all API routes
app.use('/api', apiRateLimiter);

// Log environment
if (process.env.NODE_ENV === 'production') {
    logger.info('Running in production mode');
} else {
    logger.info('Running in development mode');
}

// JSON body parsing with size limit
app.use(express.json({ limit: '1mb' }));

// Cookie parsing
app.use(cookieParser());


// --- Static File Serving ---

// User uploads (e.g. profile photos)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Admin API routes are handled by '/api/admin' - no static admin UI files are served from this backend anymore
// The admin UI is now fully implemented in the Next.js frontend


// Public SPA (Single Page Application) - Serve from 'public' directory
app.use(express.static(path.join(__dirname, 'public')));
// Fallback for SPA routing (requests not matching /api or /uploads go to index.html)
app.get('*', (req, res, next) => {
    if (req.path.startsWith('/api') ||
        req.path.startsWith('/uploads')) {
        return next(); // Skip SPA fallback for API and uploads routes
    }
    // Serve the main index.html for SPA routes
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});


// Attach Prisma Client to request object
app.use((req, _, next) => {
    req.prisma = prisma;
    next();
});


// --- API Routes ---
const userRoutes = require('./src/routes/user.routes.js');
const adminRoutes = require('./src/routes/admin.routes.js');
const analyticsRoutes = require('./src/routes/analytics');
const similarityRoutes = require('./src/routes/similarity.routes.js');
const docsRoutes = require('./src/routes/docs.js');
const healthRoutes = require('./src/routes/health.routes.js');
const matchesRoutes = require('./src/routes/matches.routes.js');
const paymentRoutes = require('./routes/payment-routes.js');
const privacyRoutes = require('./routes/privacy-routes.js');
const contactRoutes = require('./routes/contact-routes.js');

// Mount API routes
app.use('/api/users', userRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/similarity', similarityRoutes);
app.use('/api/docs', docsRoutes);
app.use('/api/health', healthRoutes);
app.use('/api/matches', matchesRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/privacy', privacyRoutes);
app.use('/api/contact', contactRoutes);

// Simple health check endpoint at root level
app.get('/health', (_, res) => {
    res.success({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    }, 'Service is healthy');
});

// Placeholder OTP auth endpoints
app.post('/api/auth/send-otp', (req, res) => {
    const { phone } = req.body;
    if (!phone) {
        return res.status(400).json({ success:false, message:'Phone number is required.' });
    }
    // TODO: Implement actual OTP generation and sending (e.g., Twilio)
    console.log(`(Simulated) OTP would be sent to ${phone}`);
    res.json({ success:true, message:'OTP sent successfully (Simulated).' });
});
app.post('/api/auth/verify-otp', (req, res) => {
    const { phone, otp } = req.body;
    if (!phone || !otp) {
        return res.status(400).json({ success:false, message:'Phone and OTP are required.' });
    }
    // TODO: Implement actual OTP verification
    console.log(`(Simulated) Verifying OTP ${otp} for ${phone}`);
    // For simulation, assume OTP '123456' is always correct
    if (otp === '123456') {
        // In a real app, you'd find/create user, generate JWT token here
        res.json({ success:true, message:'OTP verified successfully (Simulated).', token: 'dummy-jwt-token-for-simulation' });
    } else {
        res.status(401).json({ success:false, message:'Invalid OTP (Simulated).' });
    }
});

// Feature flags endpoint for testing
app.get('/api/feature-flags', (_, res) => {
    res.success({
        useRealBackend: process.env.USE_REAL_DATA === 'true',
        enableBiodataTemplates: true,
        enableSpotlightFeatures: true,
        enablePaymentGateway: process.env.ENABLE_PAYMENT_GATEWAY === 'true' || false,
        enableNotifications: true,
        enableMatchingAlgorithm: true,
        enableRedisCache: process.env.ENABLE_REDIS_CACHE === 'true' || false,
        enableDarkMode: false,
        enableAnimations: true,
        showMockDataIndicator: process.env.NODE_ENV !== 'production'
    }, "Feature flags fetched successfully.");
});

// Auth login endpoint for testing
app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;

    // Simple validation
    if (!email || !password) {
        return res.status(400).json({
            success: false,
            message: 'Email and password are required'
        });
    }

    // Mock credentials for testing
    if (email === '<EMAIL>' && password === 'password123') {
        return res.status(200).json({
            success: true,
            message: 'Login successful',
            token: 'mock-jwt-token-for-testing',
            user: {
                id: 'user1',
                email: '<EMAIL>',
                name: 'Test User',
                role: 'USER'
            }
        });
    }

    // Invalid credentials
    return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
    });
});

// Auth register endpoint for testing
app.post('/api/auth/register', (req, res) => {
    const { email, password, name } = req.body;

    // Simple validation
    if (!email || !password || !name) {
        return res.status(400).json({
            success: false,
            message: 'Email, password, and name are required'
        });
    }

    // Mock registration success
    return res.status(201).json({
        success: true,
        message: 'Registration successful',
        user: {
            id: 'new-user-id',
            email,
            name,
            role: 'USER'
        }
    });
});


// --- 404 Not Found Handler ---
// This should come after all valid routes and static file handlers
app.use((req, res) => {
    logger.warn(`404 Not Found: ${req.method} ${req.originalUrl}`, {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        requestId: req.requestId
    });

    return res.notFound(`Resource not found: ${req.method} ${req.originalUrl}`);
});


// --- Global Error Handler ---
// Import the error handler middleware
const { errorHandler } = require('./src/middleware/errorHandler.middleware');
app.use(errorHandler);


// --- Graceful Shutdown ---
const gracefulShutdown = async (signal) => {
    logger.info(`Received ${signal}. Initiating graceful shutdown...`);

    // Stop ML Service first
    stopMLService();

    // If server is running, close it
    if (httpServer) {
        // Close Socket.IO connections first
        if (io) {
            logger.info('Closing Socket.IO connections...');
            io.close((err) => {
                if (err) {
                    logger.error(`Error closing Socket.IO: ${err.message}`);
                } else {
                    logger.info('Socket.IO server closed.');
                }
            });
        }

        // Now close the HTTP server
        logger.info('Closing HTTP server...');
        httpServer.close(async () => {
            logger.info('HTTP server closed.');

            // Disconnect Prisma Client
            try {
                logger.info('Disconnecting Prisma Client...');
                await prisma.$disconnect();
                logger.info('Prisma Client disconnected successfully.');
            } catch (disconnectErr) {
                logger.error(`Error disconnecting Prisma: ${disconnectErr.message}`);
            } finally {
                logger.info('Graceful shutdown completed.');
                process.exit(0); // Exit process
            }
        });

        // Set a timeout for forceful shutdown if graceful shutdown takes too long
        setTimeout(() => {
            logger.error('Graceful shutdown timed out. Forcing exit.');
            process.exit(1);
        }, 10000); // 10 seconds timeout
    } else {
        // If server didn't start, just disconnect Prisma
        try {
            await prisma.$disconnect();
            logger.info('Prisma Client disconnected (server was not running).');
        } catch (disconnectErr) {
            logger.error(`Error disconnecting Prisma (server not running): ${disconnectErr.message}`);
        } finally {
            process.exit(0);
        }
    }
};

// Listen for termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT')); // Ctrl+C
process.on('SIGTERM', () => gracefulShutdown('SIGTERM')); // Kill command

// Handle uncaught exceptions and unhandled rejections
process.on('uncaughtException', (err) => {
    logger.error(`Uncaught Exception: ${err.message}`, { stack: err.stack });
    gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', {
        promise: promise,
        reason: reason
    });
    gracefulShutdown('unhandledRejection');
});


// Import scheduled tasks
const { initScheduledTasks } = require('./src/tasks/scheduledTasks');

// Import feature seed script
const seedFeatures = require('./prisma/seed-features');
const bcrypt = require('bcrypt');

// Import SMS service
const msg91Service = require('./src/services/sms/msg91.service');

// Import child_process for starting ML service
const { spawn } = require('child_process');
const axios = require('axios');
let mlServiceProcess = null;

// Function to start ML service asynchronously (FIXED: No more 60s timeout)
const startMLService = async () => {
    try {
        logger.info('🚀 Starting Python ML Service (Async Mode)...');

        mlServiceProcess = spawn('python', ['src/api/matching_api.py'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                ML_SERVICE_PORT: '5000',
                PYTORCH_DISABLE_WARNINGS: '1',
                OMP_NUM_THREADS: '2' // Limit CPU usage for faster startup
            }
        });

        let mlServiceReady = false;
        let initializationStarted = false;

        mlServiceProcess.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                logger.info(`ML Service: ${output}`);

                // Track initialization progress
                if (output.includes('Initializing PyTorch') || output.includes('Starting ML Service')) {
                    initializationStarted = true;
                    logger.info('🧠 PyTorch model initialization started in background...');
                }

                // Check if service is ready
                if (output.includes('Running on') || output.includes('Built new model') || output.includes('ML Service starting')) {
                    mlServiceReady = true;
                    logger.info('✅ ML Service HTTP server is ready!');
                }
            }
        });

        mlServiceProcess.stderr.on('data', (data) => {
            const error = data.toString().trim();
            // Filter out Flask debug messages and normal startup messages
            if (error &&
                !error.includes('WARNING') &&
                !error.includes('Tip:') &&
                !error.includes('Restarting with stat') &&
                !error.includes('Debugger is active') &&
                !error.includes('Debugger PIN') &&
                !error.includes('Press CTRL+C to quit') &&
                !error.includes('Running on') &&
                !error.includes('Debug mode:')) {
                logger.error(`ML Service Error: ${error}`);
            }
        });

        mlServiceProcess.on('close', (code) => {
            if (code !== 0) {
                logger.error(`ML Service exited with code ${code}`);
            } else {
                logger.info('ML Service stopped gracefully');
            }
            mlServiceProcess = null;
        });

        mlServiceProcess.on('error', (error) => {
            logger.error(`Failed to start ML Service: ${error.message}`);
            mlServiceProcess = null;
        });

        // FIXED: Don't wait for ML service - let it initialize in background
        logger.info('⏳ ML Service starting in background...');
        logger.info('🚀 Server will continue while ML service initializes');
        logger.info('🧠 PyTorch 2-Tower Model will be ready shortly...');

        // Check periodically if ML service is ready (non-blocking)
        const checkMLService = setInterval(async () => {
            try {
                const response = await axios.get('http://localhost:5000/health', {
                    timeout: 2000,
                    validateStatus: () => true
                });

                if (response.status === 200 && response.data?.success) {
                    logger.info('✅ ML Service health check passed!');
                    logger.info('🎯 Advanced matching algorithms are now operational!');
                    clearInterval(checkMLService);
                }
            } catch (e) {
                // Service not ready yet, continue checking silently
            }
        }, 5000); // Check every 5 seconds

        // Stop checking after 5 minutes and log status
        setTimeout(() => {
            clearInterval(checkMLService);
            if (!mlServiceReady) {
                logger.warn('⚠️ ML Service initialization taking longer than expected');
                logger.warn('💡 Check ML service logs for potential issues');
                logger.info('🔧 Server is fully functional, ML features will be available once service starts');
            }
        }, 300000); // 5 minutes

        return true; // Return immediately, don't wait for ML service

    } catch (error) {
        logger.error(`Error starting ML Service: ${error.message}`);
        return false;
    }
};

// Function to stop ML service
const stopMLService = () => {
    if (mlServiceProcess) {
        logger.info('🛑 Stopping ML Service...');
        mlServiceProcess.kill('SIGTERM');
        mlServiceProcess = null;
    }
};

// --- Start Server ---
const startServer = async () => {
    try {
        // Connect Prisma Client
        logger.info('Connecting to Prisma Client...');
        await prisma.$connect();
        logger.info('Prisma Client connected successfully.');

        // Create test admin user if it doesn't exist (only in development)
        if (process.env.NODE_ENV !== 'production') {
            try {
                const adminEmail = '<EMAIL>';
                const existingAdmin = await prisma.admin.findUnique({
                    where: { email: adminEmail }
                });

                if (!existingAdmin) {
                    logger.info('Creating test admin user...');
                    const hashedPassword = await bcrypt.hash('admin123', 10);
                    await prisma.admin.create({
                        data: {
                            email: adminEmail,
                            password: hashedPassword,
                            role: 'SUPER_ADMIN'
                        }
                    });
                    logger.info('Test admin user created successfully.');
                }
            } catch (adminError) {
                logger.error(`Error creating test admin user: ${adminError.message}`);
                // Continue with server startup even if admin creation fails
            }
        }

        // Seed features and access rules if needed
        if (process.env.SEED_FEATURES === 'true') {
            try {
                logger.info('Seeding features and access rules...');
                await seedFeatures.main();
                logger.info('Features and access rules seeded successfully.');
            } catch (seedError) {
                logger.error(`Error seeding features: ${seedError.message}`);
                // Continue with server startup even if seeding fails
            }
        }

        // Initialize scheduled tasks
        initScheduledTasks();
        logger.info('Scheduled tasks initialized.');

        // Initialize MSG91 service
        msg91Service.initialize({
            apiKey: process.env.MSG91_API_KEY,
            senderId: process.env.MSG91_SENDER_ID,
            dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID
        });
        logger.info('MSG91 service initialized.');

        // Start ML Service and wait for it to be ready
        if (process.env.ENABLE_ML_SERVICE !== 'false') {
            await startMLService();
        } else {
            logger.info('ML Service disabled via environment variable');
        }

        // Start HTTP server (which includes Express app and Socket.IO)
        httpServer.listen(PORT, () => {
            logger.info(`Server running successfully on http://localhost:${PORT}`);
            logger.info(`API base URL: http://localhost:${PORT}/api`);
            logger.info(`Socket.IO listening on port ${PORT}`);
            logger.info(`Frontend expected at ${FRONTEND_ORIGIN}`);

            // Log environment-specific information
            if (process.env.NODE_ENV === 'production') {
                logger.info('Running in PRODUCTION mode with optimized settings');
            } else {
                logger.info('Running in DEVELOPMENT mode with debugging enabled');
            }
        });

    } catch (error) {
        logger.error(`Failed to connect Prisma Client or start server: ${error.message}`, {
            stack: error.stack
        });

        try {
            await prisma.$disconnect(); // Attempt disconnect even on startup failure
        } catch (disconnectErr) {
            logger.error(`Error disconnecting Prisma on startup failure: ${disconnectErr.message}`);
        }

        process.exit(1); // Exit with error code
    }
};

// Run the server start function
startServer();
# Vaivahik Matrimony App - Production Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the Vaivahik Matrimony App to production with smooth transition from mock data to real backend services.

## Pre-Deployment Checklist

### ✅ Backend Services
- [ ] PostgreSQL database setup and configured
- [ ] Redis server setup for caching and sessions
- [ ] Backend API server running on port 8000
- [ ] ML Matching service operational
- [ ] Socket.IO server configured

### ✅ External Services
- [ ] Razorpay payment gateway configured
- [ ] MSG91 SMS service setup
- [ ] Brevo email service configured
- [ ] Google Maps API key obtained
- [ ] Domain and SSL certificates ready

### ✅ Environment Configuration
- [ ] Production environment variables configured
- [ ] Database migrations completed
- [ ] Security keys and secrets generated
- [ ] CORS settings configured

## Step-by-Step Deployment

### 1. Environment Setup

1. **Copy environment configuration:**
   ```bash
   cp .env.example .env.production
   ```

2. **Configure production environment variables:**
   ```bash
   # Set production URLs
   NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/api
   NEXT_PUBLIC_DOMAIN=yourdomain.com
   
   # Enable real backend services
   NEXT_PUBLIC_USE_REAL_BACKEND=true
   NEXT_PUBLIC_USE_REAL_AUTH=true
   NEXT_PUBLIC_USE_REAL_DATABASE=true
   
   # Configure database
   DATABASE_URL=************************************/vaivahik_prod
   REDIS_URL=redis://host:6379
   ```

### 2. Database Setup

1. **Create production database:**
   ```sql
   CREATE DATABASE vaivahik_prod;
   CREATE USER vaivahik_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE vaivahik_prod TO vaivahik_user;
   ```

2. **Run database migrations:**
   ```bash
   cd vaivahik-backend
   npm run migrate:prod
   ```

### 3. Backend Deployment

1. **Install dependencies:**
   ```bash
   cd vaivahik-backend
   npm install --production
   ```

2. **Start backend services:**
   ```bash
   # Start main API server
   npm run start:prod
   
   # Start ML service (in separate terminal)
   npm run start:ml
   ```

### 4. Frontend Deployment

1. **Install dependencies:**
   ```bash
   cd vaivahik-nextjs
   npm install --production
   ```

2. **Build for production:**
   ```bash
   npm run build
   ```

3. **Start production server:**
   ```bash
   npm run start
   ```

### 5. Service Configuration

#### Razorpay Setup
```javascript
// Configure in .env.production
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_live_xxxxxxxxxx
RAZORPAY_KEY_SECRET=your_secret_key
```

#### MSG91 SMS Setup
```javascript
MSG91_AUTH_KEY=306231AKuLgqtf65d32ba2P1
MSG91_TEMPLATE_ID=682f997ed6fc0577ee4f6002
```

#### Brevo Email Setup
```javascript
BREVO_API_KEY=your_brevo_api_key
BREVO_SMTP_HOST=smtp-relay.brevo.com
BREVO_SMTP_PORT=587
```

### 6. Smooth Mock-to-Real Data Transition

#### Option 1: Gradual Transition (Recommended)
1. **Start with mock data enabled:**
   ```bash
   NEXT_PUBLIC_USE_REAL_BACKEND=false
   ```

2. **Enable services one by one:**
   ```bash
   # Enable authentication first
   NEXT_PUBLIC_USE_REAL_AUTH=true
   
   # Then enable database
   NEXT_PUBLIC_USE_REAL_DATABASE=true
   
   # Finally enable all services
   NEXT_PUBLIC_USE_REAL_BACKEND=true
   ```

#### Option 2: Admin Panel Toggle
1. **Access admin panel:** `https://yourdomain.com/admin`
2. **Navigate to Production Readiness Dashboard**
3. **Use the "Data Source Control" toggle to switch between mock and real data**

### 7. Algorithm Settings Configuration

1. **Access Algorithm Settings:** `https://yourdomain.com/admin/algorithm-settings`

2. **Configure Two-Tower Model:**
   - Embedding Size: 128
   - Learning Rate: 0.001
   - Batch Size: 64
   - Epochs: 10
   - Similarity Metric: Cosine

3. **Set Matching Weights:**
   - Age Weight: 8
   - Height Weight: 6
   - Education Weight: 7
   - Occupation Weight: 7
   - Location Weight: 8
   - Caste Weight: 9
   - Sub-caste Weight: 5

4. **Enable A/B Testing:**
   - Enable A/B Testing: Yes
   - Distribution: 50/50
   - Test Duration: 30 days

### 8. Production Monitoring

#### Health Checks
- API Health: `https://api.yourdomain.com/health`
- ML Service: `https://api.yourdomain.com/api/matches/health`
- Database: Monitor connection status
- Redis: Monitor cache performance

#### Performance Monitoring
- Response times < 2 seconds
- Database query optimization
- Redis cache hit ratio > 80%
- ML model inference time < 500ms

### 9. Security Configuration

1. **SSL/TLS Setup:**
   ```bash
   USE_SSL=true
   ```

2. **CORS Configuration:**
   ```bash
   CORS_ORIGIN=https://yourdomain.com
   ```

3. **Rate Limiting:**
   ```bash
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   ```

4. **Contact Reveal Security:**
   ```bash
   ENABLE_CONTACT_REVEAL_SECURITY=true
   FRAUD_DETECTION_THRESHOLD=80
   ```

### 10. Post-Deployment Verification

#### Functional Tests
- [ ] User registration and login
- [ ] Profile creation and editing
- [ ] AI matching functionality
- [ ] Payment processing
- [ ] SMS and email notifications
- [ ] Chat functionality
- [ ] Admin panel access

#### Performance Tests
- [ ] Page load times < 3 seconds
- [ ] API response times < 2 seconds
- [ ] Database query performance
- [ ] ML model inference speed

#### Security Tests
- [ ] SSL certificate validation
- [ ] Authentication flows
- [ ] Authorization checks
- [ ] Rate limiting functionality
- [ ] Input validation

## Algorithm Settings Explanation

### Two-Tower Model Configuration

The Two-Tower model is an advanced machine learning architecture for matrimonial matching:

#### **Model Architecture:**
- **User Tower:** Processes user profile features
- **Match Tower:** Processes potential match features
- **Similarity Computation:** Calculates compatibility scores

#### **Key Parameters:**

1. **Embedding Size (128):**
   - Dimensionality of user and match representations
   - Higher values = more detailed representations
   - Recommended: 128-256 for matrimonial data

2. **Learning Rate (0.001):**
   - Controls how fast the model learns
   - Too high = unstable training
   - Too low = slow convergence

3. **Batch Size (64):**
   - Number of samples processed together
   - Affects training speed and memory usage
   - Recommended: 32-128 for matrimonial data

4. **Similarity Metric (Cosine):**
   - **Cosine:** Measures angle between vectors (recommended)
   - **Dot Product:** Measures magnitude and direction
   - **Euclidean:** Measures distance between points

#### **Matching Weights:**
- **Caste Weight (9):** Highest priority for Maratha community
- **Age Weight (8):** Important for compatibility
- **Location Weight (8):** Proximity preference
- **Education/Occupation (7):** Professional compatibility
- **Height Weight (6):** Physical preference
- **Income Weight (5):** Financial compatibility

#### **A/B Testing:**
- Tests different algorithm configurations
- Measures success rates and user satisfaction
- Helps optimize matching performance

## Troubleshooting

### Common Issues

1. **Database Connection Errors:**
   ```bash
   # Check database URL
   echo $DATABASE_URL
   
   # Test connection
   psql $DATABASE_URL -c "SELECT 1;"
   ```

2. **Redis Connection Issues:**
   ```bash
   # Test Redis connection
   redis-cli ping
   ```

3. **API Service Unavailable:**
   ```bash
   # Check backend server status
   curl https://api.yourdomain.com/health
   ```

4. **ML Service Not Responding:**
   ```bash
   # Check ML service
   curl https://api.yourdomain.com/api/matches/health
   ```

### Support

For deployment support, contact:
- Technical Support: <EMAIL>
- Documentation: https://docs.vaivahik.com
- GitHub Issues: https://github.com/vaivahik/issues

## Maintenance

### Regular Tasks
- Database backups (daily)
- Log rotation (weekly)
- Security updates (monthly)
- Performance monitoring (continuous)
- ML model retraining (quarterly)

### Scaling Considerations
- Horizontal scaling for API servers
- Database read replicas
- Redis clustering
- CDN for static assets
- Load balancing

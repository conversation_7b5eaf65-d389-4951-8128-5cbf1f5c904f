/**
 * Family Details Form Component
 * Collects comprehensive family information for profile completion
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  FormControl,
  FormLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Button,
  Chip,
  Divider,
  Alert,
  InputAdornment,
  FormHelperText
} from '@mui/material';

// Temporary icons while MUI icons package installs
const FamilyIcon = () => <span>👨‍👩‍👧‍👦</span>;
const HomeIcon = () => <span>🏠</span>;
const WorkIcon = () => <span>💼</span>;
const PhoneIcon = () => <span>📞</span>;
const LocationIcon = () => <span>📍</span>;

const FAMILY_TYPES = ['Nuclear', 'Joint', 'Extended'];
const FAMILY_VALUES = ['Traditional', 'Moderate', 'Liberal'];
const PARENT_STATUS = ['Both Alive', 'Father Alive', 'Mother Alive', 'Both Passed Away'];
const SIBLING_COUNT = ['0', '1', '2', '3', '4', '5+'];
const OCCUPATION_TYPES = [
  'Private Sector',
  'Government/Public Sector',
  'Business/Self Employed',
  'Civil Services',
  'Defence',
  'Retired',
  'Not Working',
  'Other'
];

const FamilyDetailsForm = ({ formData, setFormData, onNext, onPrevious }) => {
  const [errors, setErrors] = useState({});

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.familyType) newErrors.familyType = 'Family type is required';
    if (!formData.familyValues) newErrors.familyValues = 'Family values are required';
    if (!formData.parentStatus) newErrors.parentStatus = 'Parent status is required';
    if (!formData.totalSiblings) newErrors.totalSiblings = 'Number of siblings is required';
    if (!formData.fatherOccupation) newErrors.fatherOccupation = 'Father\'s occupation is required';
    if (!formData.motherOccupation) newErrors.motherOccupation = 'Mother\'s occupation is required';
    if (!formData.familyIncome) newErrors.familyIncome = 'Family income is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <FamilyIcon />
        <Typography variant="h4" gutterBottom>
          Family Details
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Tell us about your family background and values
        </Typography>
      </Box>

      <Card>
        <CardContent sx={{ p: 4 }}>
          <Grid container spacing={3}>
            {/* Family Type */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.familyType}>
                <FormLabel sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                  <HomeIcon />
                  <Box sx={{ ml: 1 }}>Family Type*</Box>
                </FormLabel>
                <Select
                  value={formData.familyType || ''}
                  onChange={handleChange('familyType')}
                  displayEmpty
                >
                  <MenuItem value="">Select family type</MenuItem>
                  {FAMILY_TYPES.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
                {errors.familyType && <FormHelperText>{errors.familyType}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Family Values */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.familyValues}>
                <FormLabel sx={{ mb: 1 }}>Family Values*</FormLabel>
                <Select
                  value={formData.familyValues || ''}
                  onChange={handleChange('familyValues')}
                  displayEmpty
                >
                  <MenuItem value="">Select family values</MenuItem>
                  {FAMILY_VALUES.map(value => (
                    <MenuItem key={value} value={value}>{value}</MenuItem>
                  ))}
                </Select>
                {errors.familyValues && <FormHelperText>{errors.familyValues}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Parent Status */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.parentStatus}>
                <FormLabel sx={{ mb: 1 }}>Parent Status*</FormLabel>
                <Select
                  value={formData.parentStatus || ''}
                  onChange={handleChange('parentStatus')}
                  displayEmpty
                >
                  <MenuItem value="">Select parent status</MenuItem>
                  {PARENT_STATUS.map(status => (
                    <MenuItem key={status} value={status}>{status}</MenuItem>
                  ))}
                </Select>
                {errors.parentStatus && <FormHelperText>{errors.parentStatus}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Total Siblings */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.totalSiblings}>
                <FormLabel sx={{ mb: 1 }}>Total Siblings*</FormLabel>
                <Select
                  value={formData.totalSiblings || ''}
                  onChange={handleChange('totalSiblings')}
                  displayEmpty
                >
                  <MenuItem value="">Select number of siblings</MenuItem>
                  {SIBLING_COUNT.map(count => (
                    <MenuItem key={count} value={count}>{count}</MenuItem>
                  ))}
                </Select>
                {errors.totalSiblings && <FormHelperText>{errors.totalSiblings}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Married Siblings */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <FormLabel sx={{ mb: 1 }}>Married Siblings</FormLabel>
                <Select
                  value={formData.marriedSiblings || ''}
                  onChange={handleChange('marriedSiblings')}
                  displayEmpty
                >
                  <MenuItem value="">Select married siblings</MenuItem>
                  {SIBLING_COUNT.map(count => (
                    <MenuItem key={count} value={count}>{count}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Parent Details
              </Typography>
            </Grid>

            {/* Father's Occupation */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.fatherOccupation}>
                <FormLabel sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                  <WorkIcon />
                  <Box sx={{ ml: 1 }}>Father's Occupation*</Box>
                </FormLabel>
                <Select
                  value={formData.fatherOccupation || ''}
                  onChange={handleChange('fatherOccupation')}
                  displayEmpty
                >
                  <MenuItem value="">Select father's occupation</MenuItem>
                  {OCCUPATION_TYPES.map(occupation => (
                    <MenuItem key={occupation} value={occupation}>{occupation}</MenuItem>
                  ))}
                </Select>
                {errors.fatherOccupation && <FormHelperText>{errors.fatherOccupation}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Mother's Occupation */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.motherOccupation}>
                <FormLabel sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                  <WorkIcon />
                  <Box sx={{ ml: 1 }}>Mother's Occupation*</Box>
                </FormLabel>
                <Select
                  value={formData.motherOccupation || ''}
                  onChange={handleChange('motherOccupation')}
                  displayEmpty
                >
                  <MenuItem value="">Select mother's occupation</MenuItem>
                  {OCCUPATION_TYPES.map(occupation => (
                    <MenuItem key={occupation} value={occupation}>{occupation}</MenuItem>
                  ))}
                </Select>
                {errors.motherOccupation && <FormHelperText>{errors.motherOccupation}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Family Income */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.familyIncome}>
                <FormLabel sx={{ mb: 1 }}>Family Income (Annual)*</FormLabel>
                <TextField
                  value={formData.familyIncome || ''}
                  onChange={handleChange('familyIncome')}
                  placeholder="e.g., 10-15 Lakhs"
                  error={!!errors.familyIncome}
                  helperText={errors.familyIncome}
                />
              </FormControl>
            </Grid>

            {/* Family Contact */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <FormLabel sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                  <PhoneIcon />
                  <Box sx={{ ml: 1 }}>Family Contact Number</Box>
                </FormLabel>
                <TextField
                  value={formData.familyContact || ''}
                  onChange={handleChange('familyContact')}
                  placeholder="Alternative family contact"
                  InputProps={{
                    startAdornment: <InputAdornment position="start">+91</InputAdornment>
                  }}
                />
              </FormControl>
            </Grid>

            {/* Family Location */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <FormLabel sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                  <LocationIcon />
                  <Box sx={{ ml: 1 }}>Family Location</Box>
                </FormLabel>
                <TextField
                  value={formData.familyLocation || ''}
                  onChange={handleChange('familyLocation')}
                  placeholder="City where family resides"
                />
              </FormControl>
            </Grid>

            {/* About Family */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <FormLabel sx={{ mb: 1 }}>About Family</FormLabel>
                <TextField
                  multiline
                  rows={3}
                  value={formData.aboutFamily || ''}
                  onChange={handleChange('aboutFamily')}
                  placeholder="Tell us about your family background, traditions, and values..."
                />
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              onClick={onPrevious}
              size="large"
            >
              Previous
            </Button>
            <Button
              variant="contained"
              onClick={handleSubmit}
              size="large"
              sx={{ minWidth: 120 }}
            >
              Next
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default FamilyDetailsForm;
